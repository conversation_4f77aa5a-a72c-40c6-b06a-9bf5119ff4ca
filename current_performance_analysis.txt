MoviePy 2.1.2 性能分析报告
==================================================

完整统计信息:
         884751 function calls (833068 primitive calls) in 78.905 seconds

   Ordered by: cumulative time
   List reduced from 1171 to 20 due to restriction <20>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
        1    0.005    0.005   78.842   78.842 E:\repos\jingwu-hero\enhanced_generator.py:544(demo_enhanced_features)
        1    0.003    0.003   78.824   78.824 E:\repos\jingwu-hero\enhanced_generator.py:415(generate_bilingual_video)
 22450/53    0.127    0.000   73.651    1.390 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\decorators.py:89(wrapper)
        1    0.000    0.000   73.504   73.504 E:\repos\jingwu-hero\enhanced_generator.py:333(_finalize_and_export_video)
        1    0.000    0.000   73.476   73.476 <decorator-gen-62>:1(write_videofile)
    447/1    0.001    0.000   73.476   73.476 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\decorators.py:47(requires_duration)
        1    0.000    0.000   73.476   73.476 <decorator-gen-61>:1(write_videofile)
      2/1    0.000    0.000   73.476   73.476 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\decorators.py:143(wrapper)
        1    0.000    0.000   73.476   73.476 <decorator-gen-60>:1(write_videofile)
        1    0.000    0.000   73.476   73.476 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\decorators.py:19(convert_masks_to_RGB)
        1    0.000    0.000   73.476   73.476 <decorator-gen-59>:1(write_videofile)
        1    0.000    0.000   73.476   73.476 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:204(write_videofile)
        1    1.565    1.565   72.885   72.885 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\io\ffmpeg_writer.py:242(ffmpeg_write_video)
13772/1418    0.022    0.000   66.538    0.047 <decorator-gen-17>:1(get_frame)
13772/1418    0.357    0.000   66.511    0.047 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\Clip.py:66(get_frame)
      960    3.699    0.004   65.989    0.069 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\compositing\CompositeVideoClip.py:130(frame_function)
      481    0.103    0.000   43.921    0.091 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\Clip.py:510(iter_frames)
     1904    4.283    0.002   32.417    0.017 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:720(compose_on)
     1904   17.467    0.009   21.732    0.011 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:796(compose_mask)
     7557   18.287    0.002   18.287    0.002 {method 'astype' of 'numpy.ndarray' objects}




PIL相关:
         884751 function calls (833068 primitive calls) in 78.905 seconds

   Ordered by: cumulative time
   List reduced from 1171 to 171 due to restriction <'PIL'>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
     1904    0.012    0.000    6.012    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3579(alpha_composite)
     4306    0.028    0.000    5.967    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:927(convert)
     1904    5.946    0.003    5.946    0.003 {built-in method PIL._imaging.alpha_composite}
     2564    0.011    0.000    4.176    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:557(text)
     2564    0.028    0.000    4.153    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:612(draw_text)
     4288    0.126    0.000    4.150    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3241(fromarray)
     6202    0.043    0.000    4.072    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3059(new)
     4288    0.024    0.000    4.018    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3157(frombuffer)
     8766    3.962    0.000    3.962    0.000 {built-in method PIL._imaging.fill}
     2384    0.019    0.000    3.907    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3111(frombytes)
     2564    0.006    0.000    2.692    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:549(getmask2)
     2384    0.015    0.000    1.626    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:843(frombytes)
      489    0.144    0.000    1.422    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:726(__array_interface__)
      489    0.030    0.000    1.270    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:764(tobytes)
     1904    0.012    0.000    1.093    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1935(putalpha)
     1905    0.005    0.000    0.732    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1263(copy)
     1904    0.017    0.000    0.706    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1728(paste)
     17/9    0.006    0.000    0.698    0.078 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:2210(resize)
    14328    0.096    0.000    0.135    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:588(_new)
    18147    0.028    0.000    0.070    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:880(load)
        1    0.000    0.000    0.042    0.042 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1355(filter)
    20531    0.037    0.000    0.042    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:543(__init__)
        1    0.000    0.000    0.042    0.042 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFilter.py:187(filter)
     2385    0.024    0.000    0.039    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:414(_getdecoder)
    36774    0.020    0.000    0.033    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:553(im)
        8    0.000    0.000    0.029    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:803(truetype)
    12874    0.015    0.000    0.027    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3040(_check_size)
     2564    0.005    0.000    0.026    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:635(fill)
        1    0.000    0.000    0.026    0.026 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3449(open)
        1    0.000    0.000    0.025    0.025 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:338(preinit)
     3808    0.005    0.000    0.022    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:640(_ensure_mutable)
       16    0.000    0.000    0.017    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:876(freetype)
       16    0.000    0.000    0.017    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:217(__init__)
    21962    0.015    0.000    0.015    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:580(readonly)
     1904    0.013    0.000    0.013    0.000 {built-in method PIL._imaging.map_buffer}
        2    0.000    0.000    0.010    0.005 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:29(enhance)
        2    0.000    0.000    0.010    0.005 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3594(blend)
        2    0.010    0.005    0.010    0.005 {built-in method PIL._imaging.blend}
     2564    0.002    0.000    0.009    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:605(getink)
     2565    0.003    0.000    0.008    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3428(_decompression_bomb_check)
      489    0.005    0.000    0.008    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:439(_getencoder)
     2384    0.008    0.000    0.008    0.000 {built-in method PIL._imaging.raw_decoder}
        1    0.000    0.000    0.007    0.007 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:1(<module>)
     2564    0.003    0.000    0.007    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:145(_getink)
      489    0.005    0.000    0.007    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:244(_conv_type_shape)
        1    0.000    0.000    0.007    0.007 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:71(__init__)
        1    0.000    0.000    0.007    0.007 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:1(<module>)
        1    0.000    0.000    0.006    0.006 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:272(load)
     7650    0.004    0.000    0.006    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:564(width)
       10    0.000    0.000    0.006    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:879(textbbox)
       10    0.000    0.000    0.006    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:392(getbbox)
    22437    0.005    0.000    0.005    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:584(readonly)
    28740    0.005    0.000    0.005    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:576(mode)
     7650    0.004    0.000    0.005    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:568(height)
    25342    0.004    0.000    0.004    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:572(size)
        1    0.000    0.000    0.003    0.003 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImagePalette.py:1(<module>)
     2574    0.002    0.000    0.003    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:552(_multiline_check)
    14329    0.003    0.000    0.003    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:560(im)
       16    0.003    0.000    0.003    0.000 {built-in method PIL._imagingft.getfont}
        1    0.000    0.000    0.002    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:32(__init__)
        1    0.000    0.000    0.002    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1667(histogram)
     2574    0.001    0.000    0.002    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFont.py:72(_string_length_check)
        1    0.000    0.000    0.002    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1(<module>)
        1    0.000    0.000    0.002    0.002 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\JpegImagePlugin.py:1(<module>)
      489    0.001    0.000    0.001    0.000 {built-in method PIL._imaging.raw_encoder}
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:1(<module>)
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageMath.py:1(<module>)
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:1(<module>)
        1    0.000    0.000    0.001    0.001 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:92(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3522(_open_core)
        8    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:994(Draw)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:116(__init__)
        4    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:984(load_read)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageOps.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:756(_open)
        8    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageDraw.py:66(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:975(load_prepare)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:1(<module>)
        9    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:163(read)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:120(mean)
        5    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:197(call)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:56(LoadingStrategy)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:96(sum)
       26    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\_util.py:9(is_path)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:106(Disposal)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:412(load_prepare)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpGradientFile.py:1(<module>)
        1    0.000    0.000    0.000    0.000 {built-in method PIL._imaging.new}
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageSequence.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:127(Blend)
        2    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageMode.py:37(getmode)
        4    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:203(crc)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpPaletteFile.py:1(<module>)
        3    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3757(register_extensions)
        2    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:1382(getbands)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PaletteFile.py:1(<module>)
       14    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3746(register_extension)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\JpegPresets.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:31(Stat)
       18    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\_binary.py:94(i32be)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1018(load_end)
        6    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3681(register_open)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageChops.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:58(_dib_accept)
        8    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:150(_crc32)
        1    0.000    0.000    0.000    0.000 {built-in method PIL._imaging.zip_decoder}
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\_binary.py:60(i32le)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImagePalette.py:31(ImagePalette)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:80(GifImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:65(BmpImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:448(chunk_IHDR)
        8    0.000    0.000    0.000    0.000 {built-in method PIL._imaging.draw}
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:469(chunk_IDAT)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageMath.py:27(_Operand)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:382(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:752(PngImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageStat.py:91(count)
        4    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:687(_safe_read)
        6    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3719(register_save)
        6    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3704(register_mime)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:113(ImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:54(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:549(chunk_pHYs)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\JpegImagePlugin.py:336(JpegImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:381(PngStream)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:515(chunk_gAMA)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpPaletteFile.py:23(GimpPaletteFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:252(iTXt)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:532(chunk_sRGB)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:325(BmpRleDecoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:457(StubImageFile)
        2    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3732(register_save_all)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:158(ChunkStream)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:722(PyCodecState)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:448(StubHandler)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:811(PyDecoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:487(Parser)
        2    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:744(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:865(PyEncoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:163(PpmPlainDecoder)
        3    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\Image.py:3778(register_decoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:733(PyCodec)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:71(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:279(PngInfo)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageSequence.py:24(Iterator)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:57(PpmImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFilter.py:184(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:101(_Tile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PaletteFile.py:22(PaletteFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GifImagePlugin.py:647(_Frame)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\JpegImagePlugin.py:327(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\BmpImagePlugin.py:401(DibImageFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:25(_Enhance)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpGradientFile.py:67(GradientFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:49(_accept)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1152(_Frame)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageOps.py:417(SupportsGetMesh)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:375(_RewindState)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PpmImagePlugin.py:300(PpmDecoder)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:43(Color)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:190(close)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1128(_idat)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:1139(_fdat)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:84(Brightness)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:63(Contrast)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\GimpGradientFile.py:115(GimpGradientFile)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\PngImagePlugin.py:159(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageEnhance.py:100(Sharpness)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:347(<lambda>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\PIL\ImageFile.py:96(_tilesort)




Compose相关:
         884751 function calls (833068 primitive calls) in 78.905 seconds

   Ordered by: cumulative time
   List reduced from 1171 to 11 due to restriction <'compose'>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
     1904    4.283    0.002   32.417    0.017 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:720(compose_on)
     1904   17.467    0.009   21.732    0.011 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\video\VideoClip.py:796(compose_mask)
        1    0.000    0.000    0.004    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:29(get_single_node)
        1    0.000    0.000    0.004    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:50(compose_document)
     27/1    0.000    0.000    0.004    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:63(compose_node)
      3/1    0.000    0.000    0.004    0.004 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:117(compose_mapping_node)
       24    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:88(compose_scalar_node)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:1(<module>)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:11(Composer)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:13(__init__)
        1    0.000    0.000    0.000    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\yaml\composer.py:8(ComposerError)




get_frame相关:
         884751 function calls (833068 primitive calls) in 78.905 seconds

   Ordered by: cumulative time
   List reduced from 1171 to 3 due to restriction <'get_frame'>

   ncalls  tottime  percall  cumtime  percall filename:lineno(function)
13772/1418    0.022    0.000   66.538    0.047 <decorator-gen-17>:1(get_frame)
13772/1418    0.357    0.000   66.511    0.047 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\Clip.py:66(get_frame)
      442    0.053    0.000    0.098    0.000 E:\Miniconda3\envs\lyrc-mv\Lib\site-packages\moviepy\audio\io\readers.py:188(get_frame)


