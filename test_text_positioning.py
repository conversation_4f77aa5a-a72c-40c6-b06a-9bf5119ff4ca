#!/usr/bin/env python3
"""
测试文本定位问题
"""

from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_types import LyricStyle, LyricRect
from layout_engine import LayoutEngine, VerticalStackStrategy
from lyric_clip import LyricClip, create_lyric_clip
from font_cache import TextMetricsCache, detect_text_language

def test_text_positioning():
    """测试文本定位逻辑"""
    
    # 创建测试时间轴
    test_lyrics = [
        (0.0, '这是一句很长的中文歌词\n包含多行内容\n用来测试定位'),
        (3.5, '第二句歌词'),
    ]

    # 创建主时间轴（增强预览模式）
    main_timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language='chinese',
        style=LyricStyle(font_size=80),
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
        element_id="main_lyrics",
        priority=1
    )

    video_width, video_height = 720, 1280
    
    print('=== 布局计算 ===')
    layout_engine = LayoutEngine(VerticalStackStrategy(spacing=30))
    layout_engine.add_element(main_timeline)
    
    layout_result = layout_engine.calculate_layout(video_width, video_height)
    main_rect = layout_result.element_positions["main_lyrics"]
    
    print(f'主时间轴布局区域: {main_rect}')
    print(f'视频尺寸: {video_width}x{video_height}')
    
    print('\n=== 文本尺寸计算 ===')
    test_text = '这是一句很长的中文歌词\n包含多行内容\n用来测试定位'
    language = detect_text_language(test_text)
    text_size = TextMetricsCache.get_text_size(test_text, None, 80, language)
    
    print(f'文本内容: "{test_text}"')
    print(f'文本尺寸: {text_size}')
    print(f'语言检测: {language}')
    
    print('\n=== 定位计算分析 ===')
    # 模拟_composite_text_image中的定位逻辑
    text_height, text_width = text_size[1], text_size[0]
    
    # 当前的定位逻辑
    x = main_rect.x + (main_rect.width - text_width) // 2
    y = main_rect.y + (main_rect.height - text_height) // 2
    
    print(f'布局区域: x={main_rect.x}, y={main_rect.y}, w={main_rect.width}, h={main_rect.height}')
    print(f'文本尺寸: w={text_width}, h={text_height}')
    print(f'计算位置: x={x}, y={y}')
    
    # 检查边界
    print('\n=== 边界检查 ===')
    if x < 0:
        print(f'⚠️  文本左边界超出: x={x}')
    if y < 0:
        print(f'⚠️  文本上边界超出: y={y}')
    if x + text_width > video_width:
        print(f'⚠️  文本右边界超出: x+w={x + text_width} > {video_width}')
    if y + text_height > video_height:
        print(f'⚠️  文本下边界超出: y+h={y + text_height} > {video_height}')
    
    # 边界裁剪后的位置
    clipped_x = max(0, min(x, video_width - text_width))
    clipped_y = max(0, min(y, video_height - text_height))
    
    print(f'裁剪后位置: x={clipped_x}, y={clipped_y}')
    
    if clipped_x != x or clipped_y != y:
        print('⚠️  文本位置被裁剪，可能导致显示不完整')
    
    print('\n=== 问题分析 ===')
    print('可能的问题：')
    print('1. 布局区域计算可能过大，导致文本定位超出视频边界')
    print('2. 文本尺寸计算可能不准确')
    print('3. 居中定位逻辑可能有误')
    
    # 测试LyricClip创建
    print('\n=== LyricClip测试 ===')
    try:
        lyric_clip = create_lyric_clip(
            timelines=[main_timeline],
            layout_engine=layout_engine,
            size=(video_width, video_height),
            duration=10.0,
            fps=30
        )
        print('✅ LyricClip创建成功')
        
        # 测试帧渲染
        frame = lyric_clip.get_frame(1.0)
        print(f'✅ 帧渲染成功: {frame.shape}')
        
    except Exception as e:
        print(f'❌ LyricClip测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_text_positioning()
