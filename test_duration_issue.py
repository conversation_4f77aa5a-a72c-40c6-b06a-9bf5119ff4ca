#!/usr/bin/env python3
"""
测试新旧实现的歌词时长计算差异
"""

from lyric_content import LyricContentFactory
from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_types import LyricStyle, LyricRect

def test_duration_calculation():
    """测试歌词时长计算的差异"""

    # 创建测试时间轴
    test_lyrics = [
        (0.0, '第一句歌词'),
        (3.5, '第二句歌词'),
        (7.2, '第三句歌词'),
        (10.8, '第四句歌词')
    ]

    timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language='chinese',
        style=LyricStyle(font_size=80),
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW
    )

    # 测试新实现的时长计算
    layout_rect = LyricRect(x=0, y=100, width=720, height=200)

    print('=== 新实现的时长计算 ===')
    for t in [0.5, 2.0, 3.0, 4.0, 6.0]:
        content = LyricContentFactory.create_from_timeline(timeline, t, layout_rect)
        if content:
            print(f'时间 {t}s: 歌词="{content.text}", 持续时间={content.duration}s, 结束时间={content.end_time}s')
        else:
            print(f'时间 {t}s: 无歌词')

    print('\n=== 旧实现的时长计算 ===')
    for i, (start_time, text) in enumerate(test_lyrics):
        duration = timeline._calculate_lyric_duration(i)
        print(f'第{i+1}句: 开始={start_time}s, 持续={duration}s, 结束={start_time + duration}s')

    print('\n=== 修复后的新实现 ===')
    for t in [0.5, 2.0, 3.0, 4.0, 6.0]:
        content = LyricContentFactory.create_from_timeline(timeline, t, layout_rect)
        if content:
            print(f'时间 {t}s: 歌词="{content.text}", 持续时间={content.duration}s, 结束时间={content.end_time}s')
        else:
            print(f'时间 {t}s: 无歌词')

    print('\n=== 问题分析 ===')
    print('修复内容：')
    print('1. 使用LyricContentFactory.calculate_lyric_duration计算正确的持续时间')
    print('2. 与旧实现保持一致的时长计算逻辑')

    print('\n验证结果：')
    print('新实现现在应该与旧实现的时长计算完全一致')

if __name__ == "__main__":
    test_duration_calculation()
