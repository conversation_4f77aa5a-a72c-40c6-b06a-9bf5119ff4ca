#!/usr/bin/env python3
"""
测试OpenCV版本的LyricClip性能

对比原版预渲染实现和OpenCV直接渲染实现的性能差异
"""

import time
import numpy as np
from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_types import LyricStyle
from layout_engine import LayoutEngine, VerticalStackStrategy

# 导入两个版本的实现
from lyric_clip import create_lyric_clip as create_lyric_clip_original
from lyric_clip_opencv import create_lyric_clip_opencv

def create_test_data():
    """创建测试数据"""
    # 创建测试歌词
    test_lyrics_main = [
        (0.0, "第一句主歌词"),
        (3.0, "第二句主歌词"),
        (6.0, "第三句主歌词"),
        (9.0, "第四句主歌词"),
        (12.0, "第五句主歌词")
    ]
    
    test_lyrics_aux = [
        (1.0, "First auxiliary lyric"),
        (4.0, "Second auxiliary lyric"),
        (7.0, "Third auxiliary lyric"),
        (10.0, "Fourth auxiliary lyric"),
        (13.0, "Fifth auxiliary lyric")
    ]
    
    # 创建时间轴
    main_timeline = LyricTimeline(
        lyrics_data=test_lyrics_main,
        language="chinese",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
        style=LyricStyle(font_size=80),
        element_id="main_timeline"
    )
    
    aux_timeline = LyricTimeline(
        lyrics_data=test_lyrics_aux,
        language="english",
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
        style=LyricStyle(font_size=60),
        element_id="aux_timeline"
    )
    
    # 创建布局引擎
    layout_engine = LayoutEngine(VerticalStackStrategy(spacing=50))
    layout_engine.add_element(main_timeline)
    layout_engine.add_element(aux_timeline)
    
    return [main_timeline, aux_timeline], layout_engine

def test_creation_performance():
    """测试创建性能"""
    print("🔍 测试LyricClip创建性能...")
    
    timelines, layout_engine = create_test_data()
    size = (720, 1280)
    duration = 15.0
    fps = 30
    
    # 测试原版实现（预渲染）
    print("  测试原版实现（预渲染）...")
    start_time = time.perf_counter()
    
    try:
        original_clip = create_lyric_clip_original(
            timelines=timelines,
            layout_engine=layout_engine,
            size=size,
            duration=duration,
            fps=fps
        )
        original_creation_time = time.perf_counter() - start_time
        print(f"    ✅ 原版创建时间: {original_creation_time:.3f}秒")
    except Exception as e:
        print(f"    ❌ 原版创建失败: {e}")
        original_creation_time = float('inf')
        original_clip = None
    
    # 测试OpenCV实现
    print("  测试OpenCV实现...")
    start_time = time.perf_counter()
    
    try:
        opencv_clip = create_lyric_clip_opencv(
            timelines=timelines,
            layout_engine=layout_engine,
            size=size,
            duration=duration,
            fps=fps
        )
        opencv_creation_time = time.perf_counter() - start_time
        print(f"    ✅ OpenCV创建时间: {opencv_creation_time:.3f}秒")
    except Exception as e:
        print(f"    ❌ OpenCV创建失败: {e}")
        import traceback
        traceback.print_exc()
        opencv_creation_time = float('inf')
        opencv_clip = None
    
    # 性能对比
    if original_creation_time < float('inf') and opencv_creation_time < float('inf'):
        speedup = original_creation_time / opencv_creation_time
        print(f"    📊 创建速度提升: {speedup:.1f}倍")
    
    return original_clip, opencv_clip

def test_frame_rendering_performance(original_clip, opencv_clip):
    """测试帧渲染性能"""
    print("\n🔍 测试帧渲染性能...")
    
    test_times = [0.5, 1.5, 3.5, 6.5, 9.5, 12.5]
    
    # 测试原版实现
    if original_clip:
        print("  测试原版帧渲染...")
        original_times = []
        
        for t in test_times:
            start_time = time.perf_counter()
            try:
                frame = original_clip.get_frame(t)
                render_time = time.perf_counter() - start_time
                original_times.append(render_time)
                print(f"    时间 {t}s: {render_time:.6f}秒, 帧尺寸: {frame.shape}")
            except Exception as e:
                print(f"    时间 {t}s: 渲染失败 - {e}")
                original_times.append(float('inf'))
        
        original_avg = sum(t for t in original_times if t < float('inf')) / len([t for t in original_times if t < float('inf')])
        print(f"    📊 原版平均帧渲染时间: {original_avg:.6f}秒")
    else:
        original_avg = float('inf')
        print("    ⚠️  原版实现不可用，跳过测试")
    
    # 测试OpenCV实现
    if opencv_clip:
        print("  测试OpenCV帧渲染...")
        opencv_times = []
        
        for t in test_times:
            start_time = time.perf_counter()
            try:
                frame = opencv_clip.get_frame(t)
                render_time = time.perf_counter() - start_time
                opencv_times.append(render_time)
                print(f"    时间 {t}s: {render_time:.6f}秒, 帧尺寸: {frame.shape}")
                
                # 检查是否达到性能目标
                if render_time > 0.01:
                    print(f"      ⚠️  超过性能目标(10ms): {render_time*1000:.1f}ms")
                
            except Exception as e:
                print(f"    时间 {t}s: 渲染失败 - {e}")
                import traceback
                traceback.print_exc()
                opencv_times.append(float('inf'))
        
        opencv_avg = sum(t for t in opencv_times if t < float('inf')) / len([t for t in opencv_times if t < float('inf')])
        print(f"    📊 OpenCV平均帧渲染时间: {opencv_avg:.6f}秒")
    else:
        opencv_avg = float('inf')
        print("    ⚠️  OpenCV实现不可用，跳过测试")
    
    # 性能对比
    if original_avg < float('inf') and opencv_avg < float('inf'):
        speedup = original_avg / opencv_avg
        print(f"    📊 帧渲染速度提升: {speedup:.1f}倍")
        
        # 估算视频渲染时间
        original_video_time = original_avg * 30 * 30  # 30秒 * 30fps
        opencv_video_time = opencv_avg * 30 * 30
        
        print(f"    📊 估算30秒视频渲染时间:")
        print(f"      原版: {original_video_time:.1f}秒")
        print(f"      OpenCV: {opencv_video_time:.1f}秒")
        print(f"      提升: {original_video_time/opencv_video_time:.1f}倍")
    
    return original_avg, opencv_avg

def test_memory_usage():
    """测试内存使用情况"""
    print("\n🔍 测试内存使用情况...")
    
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    
    # 基准内存
    baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
    print(f"  基准内存使用: {baseline_memory:.1f}MB")
    
    timelines, layout_engine = create_test_data()
    
    # 测试原版内存使用
    try:
        original_clip = create_lyric_clip_original(
            timelines=timelines,
            layout_engine=layout_engine,
            size=(720, 1280),
            duration=15.0,
            fps=30
        )
        original_memory = process.memory_info().rss / 1024 / 1024
        original_delta = original_memory - baseline_memory
        print(f"  原版内存使用: {original_memory:.1f}MB (+{original_delta:.1f}MB)")
        
        # 渲染几帧测试
        for t in [1.0, 5.0, 10.0]:
            original_clip.get_frame(t)
        
        original_after_render = process.memory_info().rss / 1024 / 1024
        print(f"  原版渲染后内存: {original_after_render:.1f}MB")
        
        del original_clip
    except Exception as e:
        print(f"  原版内存测试失败: {e}")
        original_delta = 0
    
    # 重新获取基准
    import gc
    gc.collect()
    baseline_memory = process.memory_info().rss / 1024 / 1024
    
    # 测试OpenCV内存使用
    try:
        opencv_clip = create_lyric_clip_opencv(
            timelines=timelines,
            layout_engine=layout_engine,
            size=(720, 1280),
            duration=15.0,
            fps=30
        )
        opencv_memory = process.memory_info().rss / 1024 / 1024
        opencv_delta = opencv_memory - baseline_memory
        print(f"  OpenCV内存使用: {opencv_memory:.1f}MB (+{opencv_delta:.1f}MB)")
        
        # 渲染几帧测试
        for t in [1.0, 5.0, 10.0]:
            opencv_clip.get_frame(t)
        
        opencv_after_render = process.memory_info().rss / 1024 / 1024
        print(f"  OpenCV渲染后内存: {opencv_after_render:.1f}MB")
        
        del opencv_clip
    except Exception as e:
        print(f"  OpenCV内存测试失败: {e}")
        import traceback
        traceback.print_exc()
        opencv_delta = 0
    
    # 内存对比
    if original_delta > 0 and opencv_delta > 0:
        memory_improvement = (original_delta - opencv_delta) / original_delta * 100
        print(f"  📊 内存使用改善: {memory_improvement:.1f}%")

def main():
    """主测试函数"""
    print("🚀 LyricClip OpenCV vs 原版性能对比测试")
    print("=" * 60)
    
    # 测试1：创建性能
    original_clip, opencv_clip = test_creation_performance()
    
    # 测试2：帧渲染性能
    original_avg, opencv_avg = test_frame_rendering_performance(original_clip, opencv_clip)
    
    # 测试3：内存使用
    test_memory_usage()
    
    # 总结报告
    print("\n" + "=" * 60)
    print("📋 性能测试总结报告")
    print("=" * 60)
    
    # 性能目标检查
    target_frame_time = 0.01  # 10毫秒
    if opencv_avg < float('inf'):
        if opencv_avg <= target_frame_time:
            print(f"✅ 性能目标达成: {opencv_avg*1000:.1f}ms/帧 (目标: {target_frame_time*1000}ms/帧)")
        else:
            print(f"⚠️  性能目标未达成: {opencv_avg*1000:.1f}ms/帧 (目标: {target_frame_time*1000}ms/帧)")
    
    # 整体评估
    if opencv_avg < float('inf') and opencv_avg <= target_frame_time:
        print("🎉 OpenCV重构成功！可以替代原版实现")
    else:
        print("⚠️  OpenCV重构需要进一步优化")

if __name__ == "__main__":
    main()
