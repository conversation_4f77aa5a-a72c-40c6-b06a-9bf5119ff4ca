#!/usr/bin/env python3
"""
简化的OpenCV LyricClip生产环境测试

直接修改enhanced_generator.py使用OpenCV版本，然后用main.py测试
"""

import time
import os
import shutil
from pathlib import Path
import subprocess

def backup_and_modify_enhanced_generator():
    """备份并修改enhanced_generator.py使用OpenCV版本"""
    print("🔧 备份并修改enhanced_generator.py...")
    
    # 备份原文件
    backup_path = "enhanced_generator_backup.py"
    shutil.copy2("enhanced_generator.py", backup_path)
    print(f"✅ 已备份到: {backup_path}")
    
    # 读取原文件
    with open("enhanced_generator.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 简单替换：将create_lyric_clip导入改为OpenCV版本
    if "from lyric_clip import create_lyric_clip" in content:
        content = content.replace(
            "from lyric_clip import create_lyric_clip",
            "from lyric_clip_opencv import create_lyric_clip_opencv as create_lyric_clip"
        )
        print("✅ 已替换为OpenCV版本")
    else:
        print("⚠️  未找到create_lyric_clip导入，手动添加...")
        # 在文件开头添加导入
        content = "from lyric_clip_opencv import create_lyric_clip_opencv as create_lyric_clip\n" + content
    
    # 写回文件
    with open("enhanced_generator.py", 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ enhanced_generator.py修改完成")
    return backup_path

def restore_enhanced_generator(backup_path):
    """恢复enhanced_generator.py"""
    if Path(backup_path).exists():
        shutil.copy2(backup_path, "enhanced_generator.py")
        os.remove(backup_path)
        print("✅ enhanced_generator.py已恢复")

def run_main_test(test_name, duration=30, use_draft=True):
    """运行main.py测试"""
    print(f"\n🚀 运行{test_name}测试...")
    
    # 构建命令
    cmd = [
        "python", "main.py",
        "--config", "精武英雄/lrc-mv.yaml",
        "--duration", str(duration)
    ]
    
    if use_draft:
        cmd.append("--draft")
    
    print(f"   命令: {' '.join(cmd)}")
    
    start_time = time.perf_counter()
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=180  # 3分钟超时
        )
        
        render_time = time.perf_counter() - start_time
        
        if result.returncode == 0:
            print(f"✅ {test_name}成功: {render_time:.1f}秒")
            
            # 查找输出文件
            output_files = list(Path("精武英雄").glob("*.mp4"))
            if output_files:
                # 找到最新的文件
                latest_file = max(output_files, key=lambda p: p.stat().st_mtime)
                file_size = latest_file.stat().st_size / 1024 / 1024  # MB
                print(f"   输出文件: {latest_file.name} ({file_size:.1f}MB)")
                return True, render_time, file_size, latest_file
            else:
                print("⚠️  未找到输出文件")
                return True, render_time, 0, None
        else:
            print(f"❌ {test_name}失败:")
            print(f"   错误: {result.stderr}")
            if result.stdout:
                print(f"   输出: {result.stdout}")
            return False, render_time, 0, None
    
    except subprocess.TimeoutExpired:
        print(f"❌ {test_name}超时（180秒）")
        return False, 180, 0, None
    except Exception as e:
        print(f"❌ {test_name}异常: {e}")
        return False, 0, 0, None

def main():
    """主测试函数"""
    print("🚀 简化OpenCV LyricClip生产环境测试")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "main.py",
        "enhanced_generator.py",
        "lyric_clip_opencv.py",
        "精武英雄/lrc-mv.yaml"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 所有必要文件检查通过")
    
    # 清理之前的输出文件
    print("\n🧹 清理之前的输出文件...")
    for mp4_file in Path("精武英雄").glob("*.mp4"):
        if mp4_file.name not in ["精武英雄 - 甄子丹.mp4"]:  # 保留原始文件
            try:
                os.remove(mp4_file)
                print(f"   删除: {mp4_file.name}")
            except:
                pass
    
    backup_path = None
    
    try:
        # 测试原版实现
        print("\n🔍 测试原版LyricClip实现...")
        original_success, original_time, original_size, original_file = run_main_test(
            "原版LyricClip", duration=30, use_draft=True
        )
        
        # 重命名原版输出文件
        if original_file and original_file.exists():
            original_renamed = original_file.parent / f"original_{original_file.name}"
            shutil.move(str(original_file), str(original_renamed))
            print(f"   原版文件重命名为: {original_renamed.name}")
        
        # 修改为OpenCV版本
        backup_path = backup_and_modify_enhanced_generator()
        
        # 测试OpenCV实现
        print("\n🔍 测试OpenCV LyricClip实现...")
        opencv_success, opencv_time, opencv_size, opencv_file = run_main_test(
            "OpenCV LyricClip", duration=30, use_draft=True
        )
        
        # 重命名OpenCV输出文件
        if opencv_file and opencv_file.exists():
            opencv_renamed = opencv_file.parent / f"opencv_{opencv_file.name}"
            shutil.move(str(opencv_file), str(opencv_renamed))
            print(f"   OpenCV文件重命名为: {opencv_renamed.name}")
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("📋 生产环境测试报告")
        print("=" * 60)
        
        if original_success and opencv_success:
            speedup = original_time / opencv_time
            print(f"✅ 两个版本都成功运行")
            print(f"📊 性能对比:")
            print(f"   原版LyricClip: {original_time:.1f}秒 ({original_size:.1f}MB)")
            print(f"   OpenCV版本: {opencv_time:.1f}秒 ({opencv_size:.1f}MB)")
            print(f"   性能提升: {speedup:.1f}倍")
            
            # 检查输出质量
            if original_size > 0:
                size_diff = abs(original_size - opencv_size) / original_size * 100
                print(f"   文件大小差异: {size_diff:.1f}%")
                
                if size_diff < 10:
                    print("✅ 输出质量一致")
                else:
                    print("⚠️  输出质量可能有差异")
            
            success = True
        
        elif opencv_success:
            print(f"✅ OpenCV版本成功: {opencv_time:.1f}秒 ({opencv_size:.1f}MB)")
            print("⚠️  原版实现失败，无法对比")
            success = True
        
        elif original_success:
            print(f"✅ 原版实现成功: {original_time:.1f}秒 ({original_size:.1f}MB)")
            print("❌ OpenCV版本失败")
            success = False
        
        else:
            print("❌ 两个版本都失败")
            success = False
        
        # 最终结论
        if opencv_success:
            print("\n🎉 OpenCV版本生产环境测试通过！")
            print("   main.py + OpenCV LyricClip 可以正常工作")
            
            # 性能评估
            if opencv_time < 60:  # 30秒视频在60秒内完成
                print("✅ 性能表现优秀")
            elif opencv_time < 120:
                print("✅ 性能表现良好")
            else:
                print("⚠️  性能需要进一步优化")
        else:
            print("\n⚠️  OpenCV版本需要进一步调试")
        
        return success
    
    finally:
        # 恢复原始文件
        if backup_path:
            restore_enhanced_generator(backup_path)

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ 生产环境测试成功，OpenCV版本已准备好部署")
    else:
        print("\n❌ 生产环境测试失败，需要进一步调试")
