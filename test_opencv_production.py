#!/usr/bin/env python3
"""
生产环境OpenCV LyricClip集成测试

使用真实的精武英雄数据测试OpenCV版本的LyricClip在生产环境中的表现
"""

import time
import os
from pathlib import Path

def test_opencv_with_main_cli():
    """使用main.py CLI测试OpenCV版本"""
    print("🚀 生产环境OpenCV LyricClip集成测试")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "main.py",
        "config/jingwu_hero_config.yaml",
        "lyrics/jingwu_hero_chinese.lrc",
        "lyrics/jingwu_hero_english.lrc",
        "audio/jingwu_hero.mp3"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("请确保所有文件存在后重新运行测试")
        return False
    
    print("✅ 所有必要文件检查通过")
    
    # 创建临时配置文件，使用OpenCV版本
    print("\n🔧 创建OpenCV测试配置...")
    
    # 读取原始配置
    with open("config/jingwu_hero_config.yaml", 'r', encoding='utf-8') as f:
        original_config = f.read()
    
    # 创建测试配置（短时长，便于快速测试）
    test_config = original_config.replace(
        "t_max_sec: 30", "t_max_sec: 10"  # 只渲染10秒进行测试
    )
    
    test_config_path = "config/jingwu_hero_opencv_test.yaml"
    with open(test_config_path, 'w', encoding='utf-8') as f:
        f.write(test_config)
    
    print(f"✅ 测试配置已创建: {test_config_path}")
    
    # 测试原版实现（如果可用）
    print("\n🔍 测试原版实现...")
    original_output = "output/jingwu_hero_original_test.mp4"
    
    # 清理之前的输出文件
    if Path(original_output).exists():
        os.remove(original_output)
    
    start_time = time.perf_counter()
    
    # 运行原版实现
    import subprocess
    try:
        result = subprocess.run([
            "python", "main.py",
            "--config", test_config_path,
            "--output", original_output,
            "--draft"  # 使用draft模式加快测试
        ], capture_output=True, text=True, timeout=120)
        
        original_time = time.perf_counter() - start_time
        
        if result.returncode == 0:
            print(f"✅ 原版实现成功: {original_time:.1f}秒")
            original_success = True
            
            # 检查输出文件
            if Path(original_output).exists():
                file_size = Path(original_output).stat().st_size / 1024 / 1024  # MB
                print(f"   输出文件: {original_output} ({file_size:.1f}MB)")
            else:
                print("⚠️  输出文件未生成")
        else:
            print(f"❌ 原版实现失败:")
            print(f"   错误: {result.stderr}")
            original_success = False
            original_time = float('inf')
    
    except subprocess.TimeoutExpired:
        print("❌ 原版实现超时（120秒）")
        original_success = False
        original_time = float('inf')
    except Exception as e:
        print(f"❌ 原版实现异常: {e}")
        original_success = False
        original_time = float('inf')
    
    # 测试OpenCV实现
    print("\n🔍 测试OpenCV实现...")
    
    # 临时修改enhanced_generator.py使用OpenCV版本
    print("   正在集成OpenCV版本...")
    
    # 备份原始文件
    enhanced_generator_backup = None
    try:
        with open("enhanced_generator.py", 'r', encoding='utf-8') as f:
            enhanced_generator_backup = f.read()
    except Exception as e:
        print(f"⚠️  无法备份enhanced_generator.py: {e}")
    
    # 修改enhanced_generator.py使用OpenCV版本
    try:
        # 读取当前文件
        with open("enhanced_generator.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 添加OpenCV导入和替换
        opencv_import = """
# OpenCV LyricClip集成
try:
    from lyric_clip_opencv import create_lyric_clip_opencv
    OPENCV_AVAILABLE = True
    print("✅ OpenCV LyricClip可用")
except ImportError as e:
    OPENCV_AVAILABLE = False
    print(f"⚠️  OpenCV LyricClip不可用: {e}")
"""
        
        # 在文件开头添加导入
        if "from lyric_clip_opencv import" not in content:
            content = opencv_import + "\n" + content
        
        # 替换create_lyric_clip调用
        if "create_lyric_clip(" in content and "create_lyric_clip_opencv(" not in content:
            content = content.replace(
                "from lyric_clip import create_lyric_clip",
                "# from lyric_clip import create_lyric_clip  # 使用OpenCV版本"
            )
            content = content.replace(
                "create_lyric_clip(",
                "create_lyric_clip_opencv(" if "OPENCV_AVAILABLE" in content else "create_lyric_clip("
            )
        
        # 写回文件
        with open("enhanced_generator.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ OpenCV版本集成完成")
        
    except Exception as e:
        print(f"❌ OpenCV集成失败: {e}")
        return False
    
    # 运行OpenCV版本测试
    opencv_output = "output/jingwu_hero_opencv_test.mp4"
    
    # 清理之前的输出文件
    if Path(opencv_output).exists():
        os.remove(opencv_output)
    
    start_time = time.perf_counter()
    
    try:
        result = subprocess.run([
            "python", "main.py",
            "--config", test_config_path,
            "--output", opencv_output,
            "--draft"  # 使用draft模式
        ], capture_output=True, text=True, timeout=60)  # OpenCV应该更快
        
        opencv_time = time.perf_counter() - start_time
        
        if result.returncode == 0:
            print(f"✅ OpenCV实现成功: {opencv_time:.1f}秒")
            opencv_success = True
            
            # 检查输出文件
            if Path(opencv_output).exists():
                file_size = Path(opencv_output).stat().st_size / 1024 / 1024  # MB
                print(f"   输出文件: {opencv_output} ({file_size:.1f}MB)")
            else:
                print("⚠️  输出文件未生成")
        else:
            print(f"❌ OpenCV实现失败:")
            print(f"   错误: {result.stderr}")
            print(f"   输出: {result.stdout}")
            opencv_success = False
            opencv_time = float('inf')
    
    except subprocess.TimeoutExpired:
        print("❌ OpenCV实现超时（60秒）")
        opencv_success = False
        opencv_time = float('inf')
    except Exception as e:
        print(f"❌ OpenCV实现异常: {e}")
        opencv_success = False
        opencv_time = float('inf')
    
    # 恢复原始文件
    if enhanced_generator_backup:
        try:
            with open("enhanced_generator.py", 'w', encoding='utf-8') as f:
                f.write(enhanced_generator_backup)
            print("✅ enhanced_generator.py已恢复")
        except Exception as e:
            print(f"⚠️  恢复enhanced_generator.py失败: {e}")
    
    # 清理测试文件
    try:
        if Path(test_config_path).exists():
            os.remove(test_config_path)
    except:
        pass
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📋 生产环境测试报告")
    print("=" * 60)
    
    if original_success and opencv_success:
        speedup = original_time / opencv_time
        print(f"✅ 两个版本都成功运行")
        print(f"📊 性能对比:")
        print(f"   原版实现: {original_time:.1f}秒")
        print(f"   OpenCV实现: {opencv_time:.1f}秒")
        print(f"   性能提升: {speedup:.1f}倍")
        
        # 检查输出质量
        if Path(original_output).exists() and Path(opencv_output).exists():
            original_size = Path(original_output).stat().st_size
            opencv_size = Path(opencv_output).stat().st_size
            size_diff = abs(original_size - opencv_size) / original_size * 100
            
            print(f"📊 输出质量对比:")
            print(f"   文件大小差异: {size_diff:.1f}%")
            
            if size_diff < 10:
                print("✅ 输出质量一致")
            else:
                print("⚠️  输出质量可能有差异")
    
    elif opencv_success:
        print(f"✅ OpenCV版本成功运行: {opencv_time:.1f}秒")
        print("⚠️  原版实现失败，无法对比")
    
    elif original_success:
        print(f"✅ 原版实现成功: {original_time:.1f}秒")
        print("❌ OpenCV版本失败")
    
    else:
        print("❌ 两个版本都失败")
    
    # 最终结论
    if opencv_success:
        print("\n🎉 OpenCV版本生产环境测试通过！")
        print("   可以安全部署到生产环境")
        return True
    else:
        print("\n⚠️  OpenCV版本需要进一步调试")
        return False

def main():
    """主函数"""
    success = test_opencv_with_main_cli()
    
    if success:
        print("\n✅ 所有测试通过，OpenCV版本已准备好生产部署")
    else:
        print("\n❌ 测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
