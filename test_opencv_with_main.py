#!/usr/bin/env python3
"""
使用main.py CLI工具测试OpenCV LyricClip的生产环境性能

使用现有的精武英雄数据进行真实场景测试
"""

import time
import os
import shutil
from pathlib import Path
import subprocess

def create_test_config():
    """创建测试配置文件"""
    print("🔧 创建测试配置...")
    
    # 读取原始配置
    original_config_path = "精武英雄/lrc-mv.yaml"
    with open(original_config_path, 'r', encoding='utf-8') as f:
        config_content = f.read()
    
    # 创建短时长测试配置
    test_config_content = config_content + "\nt_max_sec: 30\n"  # 只渲染30秒
    
    test_config_path = "精武英雄/lrc-mv-test.yaml"
    with open(test_config_path, 'w', encoding='utf-8') as f:
        f.write(test_config_content)
    
    print(f"✅ 测试配置已创建: {test_config_path}")
    return test_config_path

def backup_enhanced_generator():
    """备份enhanced_generator.py"""
    backup_path = "enhanced_generator_backup.py"
    shutil.copy2("enhanced_generator.py", backup_path)
    print(f"✅ enhanced_generator.py已备份到: {backup_path}")
    return backup_path

def integrate_opencv_version():
    """集成OpenCV版本到enhanced_generator.py"""
    print("🔧 集成OpenCV版本...")
    
    with open("enhanced_generator.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经集成
    if "lyric_clip_opencv" in content:
        print("✅ OpenCV版本已集成")
        return True
    
    # 添加OpenCV导入
    opencv_import = '''
# OpenCV LyricClip集成
try:
    from lyric_clip_opencv import create_lyric_clip_opencv
    OPENCV_LYRIC_CLIP_AVAILABLE = True
    print("✅ OpenCV LyricClip已加载")
except ImportError as e:
    OPENCV_LYRIC_CLIP_AVAILABLE = False
    print(f"⚠️  OpenCV LyricClip加载失败: {e}")
    from lyric_clip import create_lyric_clip
'''
    
    # 在导入部分添加OpenCV导入
    if "from lyric_clip import create_lyric_clip" in content:
        content = content.replace(
            "from lyric_clip import create_lyric_clip",
            opencv_import
        )
    else:
        # 在文件开头添加
        content = opencv_import + "\n" + content
    
    # 替换create_lyric_clip调用
    if "create_lyric_clip(" in content:
        # 找到所有create_lyric_clip调用并替换
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "create_lyric_clip(" in line and "create_lyric_clip_opencv(" not in line:
                # 添加条件判断
                indent = len(line) - len(line.lstrip())
                space = ' ' * indent
                
                # 替换调用
                new_line = line.replace("create_lyric_clip(", "create_lyric_clip_opencv(")
                lines[i] = f"{space}if OPENCV_LYRIC_CLIP_AVAILABLE:\n{space}    {new_line.strip()}\n{space}else:\n{space}    {line.strip()}"
        
        content = '\n'.join(lines)
    
    # 写回文件
    with open("enhanced_generator.py", 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ OpenCV版本集成完成")
    return True

def restore_enhanced_generator(backup_path):
    """恢复enhanced_generator.py"""
    if Path(backup_path).exists():
        shutil.copy2(backup_path, "enhanced_generator.py")
        os.remove(backup_path)
        print("✅ enhanced_generator.py已恢复")

def run_test(config_path, output_suffix, timeout=120):
    """运行测试"""
    output_path = f"精武英雄/精武英雄-甄子丹-{output_suffix}.mp4"
    
    # 清理之前的输出
    if Path(output_path).exists():
        os.remove(output_path)
    
    print(f"🚀 开始渲染: {output_suffix}")
    start_time = time.perf_counter()
    
    try:
        # 切换到精武英雄目录运行
        result = subprocess.run([
            "python", "../main.py",
            "--config", "lrc-mv-test.yaml",
            "--output", f"精武英雄-甄子丹-{output_suffix}.mp4",
            "--draft"  # 使用draft模式
        ], 
        cwd="精武英雄",
        capture_output=True, 
        text=True, 
        timeout=timeout)
        
        render_time = time.perf_counter() - start_time
        
        if result.returncode == 0:
            print(f"✅ {output_suffix}渲染成功: {render_time:.1f}秒")
            
            # 检查输出文件
            if Path(output_path).exists():
                file_size = Path(output_path).stat().st_size / 1024 / 1024  # MB
                print(f"   输出文件: {file_size:.1f}MB")
                return True, render_time, file_size
            else:
                print("⚠️  输出文件未生成")
                return False, render_time, 0
        else:
            print(f"❌ {output_suffix}渲染失败:")
            print(f"   错误: {result.stderr}")
            if result.stdout:
                print(f"   输出: {result.stdout}")
            return False, render_time, 0
    
    except subprocess.TimeoutExpired:
        print(f"❌ {output_suffix}渲染超时（{timeout}秒）")
        return False, timeout, 0
    except Exception as e:
        print(f"❌ {output_suffix}渲染异常: {e}")
        return False, 0, 0

def main():
    """主测试函数"""
    print("🚀 使用main.py CLI工具测试OpenCV LyricClip")
    print("=" * 60)
    
    # 检查必要文件
    required_files = [
        "main.py",
        "精武英雄/lrc-mv.yaml",
        "精武英雄/精武英雄 - 甄子丹.lrc",
        "精武英雄/Jingwu Hero - Donnie Yen.lrc",
        "精武英雄/精武英雄 - 甄子丹.flac"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 所有必要文件检查通过")
    
    # 创建测试配置
    test_config_path = create_test_config()
    
    # 备份原始文件
    backup_path = backup_enhanced_generator()
    
    try:
        # 测试原版实现
        print("\n🔍 测试原版LyricClip实现...")
        original_success, original_time, original_size = run_test(
            test_config_path, "original", timeout=180
        )
        
        # 集成OpenCV版本
        if integrate_opencv_version():
            # 测试OpenCV实现
            print("\n🔍 测试OpenCV LyricClip实现...")
            opencv_success, opencv_time, opencv_size = run_test(
                test_config_path, "opencv", timeout=120
            )
        else:
            print("❌ OpenCV版本集成失败")
            opencv_success, opencv_time, opencv_size = False, 0, 0
        
        # 生成测试报告
        print("\n" + "=" * 60)
        print("📋 main.py CLI测试报告")
        print("=" * 60)
        
        if original_success and opencv_success:
            speedup = original_time / opencv_time
            print(f"✅ 两个版本都成功运行")
            print(f"📊 性能对比:")
            print(f"   原版LyricClip: {original_time:.1f}秒 ({original_size:.1f}MB)")
            print(f"   OpenCV版本: {opencv_time:.1f}秒 ({opencv_size:.1f}MB)")
            print(f"   性能提升: {speedup:.1f}倍")
            
            # 检查输出质量
            size_diff = abs(original_size - opencv_size) / original_size * 100 if original_size > 0 else 0
            print(f"   文件大小差异: {size_diff:.1f}%")
            
            if size_diff < 10:
                print("✅ 输出质量一致")
            else:
                print("⚠️  输出质量可能有差异")
            
            success = True
        
        elif opencv_success:
            print(f"✅ OpenCV版本成功: {opencv_time:.1f}秒 ({opencv_size:.1f}MB)")
            print("⚠️  原版实现失败，无法对比")
            success = True
        
        elif original_success:
            print(f"✅ 原版实现成功: {original_time:.1f}秒 ({original_size:.1f}MB)")
            print("❌ OpenCV版本失败")
            success = False
        
        else:
            print("❌ 两个版本都失败")
            success = False
        
        # 最终结论
        if opencv_success:
            print("\n🎉 OpenCV版本CLI测试通过！")
            print("   可以在生产环境中使用main.py + OpenCV LyricClip")
        else:
            print("\n⚠️  OpenCV版本需要进一步调试")
        
        return success
    
    finally:
        # 清理和恢复
        restore_enhanced_generator(backup_path)
        
        # 清理测试配置
        if Path(test_config_path).exists():
            os.remove(test_config_path)
            print("✅ 测试配置已清理")

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ CLI测试成功，OpenCV版本已准备好生产部署")
    else:
        print("\n❌ CLI测试失败，需要进一步调试")
