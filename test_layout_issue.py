#!/usr/bin/env python3
"""
测试布局裁切问题
"""

from lyric_timeline import LyricTimeline, LyricDisplayMode
from layout_types import LyricStyle
from layout_engine import LayoutEngine, VerticalStackStrategy

def test_layout_positioning():
    """测试布局定位问题"""
    
    # 创建测试时间轴
    test_lyrics = [
        (0.0, '第一句歌词\n这是多行歌词'),
        (3.5, '第二句歌词'),
        (7.2, '第三句歌词'),
    ]

    # 创建主时间轴（增强预览模式）
    main_timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language='chinese',
        style=LyricStyle(font_size=80),
        display_mode=LyricDisplayMode.ENHANCED_PREVIEW,
        element_id="main_lyrics",
        priority=1
    )
    
    # 创建副时间轴（简单模式）
    aux_timeline = LyricTimeline(
        lyrics_data=test_lyrics,
        language='english',
        style=LyricStyle(font_size=60),
        display_mode=LyricDisplayMode.SIMPLE_FADE,
        element_id="aux_lyrics",
        priority=2
    )

    video_width, video_height = 720, 1280
    
    print('=== 原始区域计算 ===')
    main_rect = main_timeline.calculate_required_rect(video_width, video_height)
    aux_rect = aux_timeline.calculate_required_rect(video_width, video_height)
    
    print(f'主时间轴原始区域: {main_rect}')
    print(f'副时间轴原始区域: {aux_rect}')
    print(f'视频尺寸: {video_width}x{video_height}')
    
    # 检查是否超出边界
    print('\n=== 边界检查 ===')
    if main_rect.y < 0:
        print(f'⚠️  主时间轴顶部超出边界: y={main_rect.y}')
    if main_rect.y + main_rect.height > video_height:
        print(f'⚠️  主时间轴底部超出边界: y+h={main_rect.y + main_rect.height} > {video_height}')
    if aux_rect.y < 0:
        print(f'⚠️  副时间轴顶部超出边界: y={aux_rect.y}')
    if aux_rect.y + aux_rect.height > video_height:
        print(f'⚠️  副时间轴底部超出边界: y+h={aux_rect.y + aux_rect.height} > {video_height}')
    
    print('\n=== 布局引擎处理 ===')
    layout_engine = LayoutEngine(VerticalStackStrategy(spacing=30))
    layout_engine.add_element(main_timeline)
    layout_engine.add_element(aux_timeline)
    
    # 计算布局
    layout_result = layout_engine.calculate_layout(video_width, video_height)
    
    print('布局后区域:')
    for element_id, rect in layout_result.element_positions.items():
        print(f'  {element_id}: {rect}')
        
        # 检查布局后是否仍然超出边界
        if rect.y < 0:
            print(f'    ⚠️  顶部超出边界: y={rect.y}')
        if rect.y + rect.height > video_height:
            print(f'    ⚠️  底部超出边界: y+h={rect.y + rect.height} > {video_height}')

if __name__ == "__main__":
    test_layout_positioning()
